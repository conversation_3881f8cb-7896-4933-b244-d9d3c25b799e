<template>
  <view class="detail-page">
    <scroll-view class="content" scroll-y>
      <!-- 基本信息 -->
      <view class="section">
        <view class="section-header">
          <van-icon name="info-o" />
          <text>基本信息</text>
        </view>

        <view class="info-item">
          <view class="label">代理商名称</view>
          <view class="value">{{ agentInfo.agentName }}</view>
        </view>

        <view class="info-item">
          <view class="label">代理商编号</view>
          <view class="value">{{ agentInfo.agentCode }}</view>
        </view>

        <view class="info-item">
          <view class="label">代理商名称</view>
          <view class="value">
            <van-tag v-if="agentInfo.agentType === 'enterprise'" type="primary"
              >企业</van-tag
            >
            <van-tag v-else type="success">个人</van-tag>
          </view>
        </view>

        <view class="info-item">
          <view class="label">联系电话</view>
          <view class="value">{{ agentInfo.phone }}</view>
        </view>

        <view class="info-item">
          <view class="label">企业统一信用代码</view>
          <view class="value">{{ agentInfo.creditCode }}</view>
        </view>

        <view class="info-item">
          <view class="label">所在省份</view>
          <view class="value">{{ agentInfo.provinceName }}</view>
        </view>

        <view class="info-item">
          <view class="label">所在城市</view>
          <view class="value">{{ agentInfo.cityName }}</view>
        </view>

        <view class="info-item">
          <view class="label">详细地址</view>
          <view class="value">{{ agentInfo.address }}</view>
        </view>

        <view class="info-item">
          <view class="label">主要覆盖区域</view>
          <view class="value"
            >{{ agentInfo.coverProvinceName }}
            {{ agentInfo.coverCityName }}</view
          >
        </view>

        <view class="info-item">
          <view class="label">团队人员</view>
          <view class="value">{{ agentInfo.teamMember }}</view>
        </view>
      </view>

      <!-- 意向产品 -->
      <view class="section">
        <view class="section-header">
          <van-icon name="shopping-cart-o" />
          <text>意向产品</text>
        </view>

        <view v-if="agentInfo.products && agentInfo.products.length > 0">
          <view
            v-for="(item, index) in agentInfo.products"
            :key="index"
            class="product-item"
          >
            <view class="product-name">{{ item.productName }}</view>
            <view class="product-info">
              <text
                >代理经验：{{
                  item.agentExperience === "enterprise" ? "企业" : "个人"
                }}</text
              >
            </view>
            <view class="product-info">
              <text
                >竞品经验：{{
                  item.competitorExperience === "enterprise" ? "企业" : "个人"
                }}</text
              >
            </view>
            <view class="product-info">
              <text>竞品名称：{{ item.competitorName }}</text>
            </view>
          </view>
        </view>
        <view v-else class="empty-tip"> 暂无意向产品 </view>
      </view>

      <!-- 意向终端 -->
      <view class="section">
        <view class="section-header">
          <van-icon name="shop-o" />
          <text>意向终端</text>
        </view>

        <view v-if="agentInfo.terminals && agentInfo.terminals.length > 0">
          <view
            v-for="(item, index) in agentInfo.terminals"
            :key="index"
            class="terminal-item"
          >
            <view class="terminal-name">{{ item.terminalName }}</view>
            <view class="terminal-info">
              <text>科室：{{ item.department }}</text>
            </view>
            <view class="terminal-info">
              <text>代理经验：{{ item.agentExperience }}</text>
            </view>
          </view>
        </view>
        <view v-else class="empty-tip"> 暂无意向终端 </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getAgentDetail } from "@/common/api/agentDA/index.js";

const agentId = ref("");
const agentInfo = ref({});

// 获取页面参数
const getPageOptions = () => {
  const pages = uni.getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage.options || {};
};

onMounted(() => {
  const options = getPageOptions();
  if (options.id) {
    agentId.value = options.id;
    loadAgentDetail();
  }
});

// 加载代理商详情
const loadAgentDetail = async () => {
  try {
    uni.showLoading({
      title: "加载中...",
    });

    const res = await getAgentDetail(agentId.value);

    uni.hideLoading();

    if (res.code === 200) {
      agentInfo.value = res.data;
    } else {
      uni.showToast({
        title: res.message || "加载失败",
        icon: "none",
      });
    }
  } catch (error) {
    uni.hideLoading();
    console.error("加载代理商详情失败:", error);
    uni.showToast({
      title: "网络错误",
      icon: "none",
    });
  }
};
</script>

<style lang="scss" scoped>
.detail-page {
  height: 100vh;
  background: #f8f8f8;
}

.content {
  height: 100%;
  padding: 20rpx;
}

.section {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-bottom: 1rpx solid #f0f0f0;

  text {
    margin-left: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.info-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.product-item,
.terminal-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.product-name,
.terminal-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.product-info,
.terminal-info {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.empty-tip {
  padding: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}
</style>
